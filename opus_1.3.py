#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🚀 TRON Address Generator with Balance Checker - Version 1.3
================================================================================
📝 Description:
   TRON address generator with three modes:
   1. Generate new mnemonics and derive addresses + private keys
   2. Read existing mnemonics from file and derive addresses + private keys
   3. Verify test case
   
   Plus online balance checking for TRX and USDT (TRC20)

✨ Features:
   - Generate new BIP39 mnemonics or read from file
   - Generate TRON addresses and private keys from BIP39 mnemonics
   - Check TRX and USDT balances online
   - Report non-zero balances
   - Non-interactive operation for automation
   - Fast bulk address generation

📦 Requirements:
   pip install bip_utils requests

⚠️ Security Notice:
   - This version outputs PRIVATE KEYS - handle with extreme care!
   - Keep output files secure and encrypted
   - Delete files after use if not needed
   - Never share files containing private keys

👨‍💻 Author: Based on J.Ekrami's opus_4.1
📅 Created: 2025-08-26
📅 Updated: 2025-08-26 (v1.3 - Added balance checking)

💡 Usage:
   # Generate 10 new mnemonics, addresses and check balances
   python tron_generator.py -g 10
   
   # Generate without balance check
   python tron_generator.py -g 10 --no-check
   
   # Read from file and check balances
   python tron_generator.py -f seeds.txt
   
   # Verify test case
   python tron_generator.py --verify

================================================================================
"""

import os
import sys
import argparse
import requests
import time
from datetime import datetime
from typing import List, Dict, Tuple
from decimal import Decimal

# BIP utilities for wallet generation
try:
    from bip_utils import (
        Bip39MnemonicGenerator,
        Bip39SeedGenerator,
        Bip44, Bip44Coins,
        Bip44Changes,
        Bip39WordsNum
    )
except ImportError:
    print("❌ Error: bip_utils not installed!")
    print("📦 Please install: pip install bip_utils")
    sys.exit(1)

class TronAddressGenerator:
    """TRON address generator with mnemonic, private key generation and balance checking"""
    
    # USDT Contract on TRON
    USDT_CONTRACT = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"

    def __init__(self, output_file: str = ""):
        """Initialize generator"""
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_file = output_file or f"tron_wallets_{self.timestamp}.txt"
        self.addresses = []
        self.balances = {}

    def generate_mnemonics(self, count: int) -> List[Dict]:
        """Generate new BIP39 mnemonics"""
        print(f"\n🎲 Generating {count} new mnemonics...")
        mnemonics = []
        
        for i in range(count):
            try:
                # Generate 12-word mnemonic
                mnemonic = Bip39MnemonicGenerator().FromWordsNumber(Bip39WordsNum.WORDS_NUM_12)
                words = str(mnemonic).split()
                
                mnemonics.append({
                    'line': i + 1,
                    'mnemonic': str(mnemonic),
                    'preview': f"{words[0]}...{words[-1]}"
                })
                
                if (i + 1) % 100 == 0:
                    print(f"  Generated {i + 1}/{count} mnemonics...")
                    
            except Exception as e:
                print(f"❌ Error generating mnemonic #{i+1}: {str(e)}")
        
        print(f"✅ Generated {len(mnemonics)} mnemonics")
        return mnemonics

    def load_seeds_from_file(self, input_file: str) -> List[Dict]:
        """Load and validate seed phrases from input file"""
        seeds = []

        if not os.path.exists(input_file):
            print(f"❌ Error: Input file '{input_file}' not found!")
            sys.exit(1)

        print(f"📂 Loading seeds from '{input_file}'...")

        try:
            with open(input_file, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            valid_count = 0
            for line_num, line in enumerate(lines, 1):
                # Skip empty lines and comments
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Validate 12-word mnemonic
                words = line.split()
                if len(words) != 12:
                    print(f"⚠️  Line {line_num}: Skipping (expected 12 words, got {len(words)})")
                    continue

                mnemonic = ' '.join(words)
                seeds.append({
                    'line': line_num,
                    'mnemonic': mnemonic,
                    'preview': f"{words[0]}...{words[-1]}"
                })
                valid_count += 1

            print(f"✅ Loaded {valid_count} valid seed phrases from {len(lines)} lines")

            if valid_count == 0:
                print("❌ No valid seed phrases found!")
                sys.exit(1)

            return seeds

        except Exception as e:
            print(f"❌ Error reading input file: {str(e)}")
            sys.exit(1)

    def generate_addresses(self, seeds: List[Dict]) -> None:
        """Generate TRON addresses and private keys from seeds"""
        print(f"\n🔐 Generating TRON addresses and private keys for {len(seeds)} seeds...")

        for idx, seed_data in enumerate(seeds):
            try:
                # Generate seed bytes
                seed_bytes = Bip39SeedGenerator(seed_data['mnemonic']).Generate()

                # TRON address using BIP44 derivation path: m/44'/195'/0'/0/0
                bip44_mst = Bip44.FromSeed(seed_bytes, Bip44Coins.TRON)
                bip44_acc = bip44_mst.Purpose().Coin().Account(0)
                bip44_chg = bip44_acc.Change(Bip44Changes.CHAIN_EXT)
                bip44_addr = bip44_chg.AddressIndex(0)
                
                # Get address
                tron_address = bip44_addr.PublicKey().ToAddress()
                
                # Get private key (hex format)
                private_key = bip44_addr.PrivateKey().Raw().ToHex()

                self.addresses.append({
                    'line': seed_data['line'],
                    'preview': seed_data['preview'],
                    'mnemonic': seed_data['mnemonic'],
                    'address': tron_address,
                    'private_key': private_key
                })
                
                if (idx + 1) % 100 == 0:
                    print(f"  Processed {idx + 1}/{len(seeds)} addresses...")

            except Exception as e:
                print(f"❌ Error generating address for seed #{seed_data['line']}: {str(e)}")

        print(f"\n📊 Successfully generated {len(self.addresses)} TRON addresses with private keys")

    def check_balances_online(self) -> None:
        """Check TRX and USDT balances for all addresses"""
        print(f"\n🌐 Checking balances online for {len(self.addresses)} addresses...")
        
        found_count = 0
        
        for idx, addr_data in enumerate(self.addresses):
            address = addr_data['address']
            
            try:
                # Check TRX balance
                trx_balance = self._get_trx_balance(address)
                
                # Check USDT balance
                usdt_balance = self._get_usdt_balance(address)
                
                # Store balances
                self.balances[address] = {
                    'trx': trx_balance,
                    'usdt': usdt_balance
                }
                
                # Report if non-zero
                if trx_balance > 0 or usdt_balance > 0:
                    found_count += 1
                    print(f"\n💰 FOUND! Address: {address}")
                    print(f"   TRX: {trx_balance:.6f}")
                    print(f"   USDT: {usdt_balance:.6f}")
                
                # Progress update
                if (idx + 1) % 50 == 0:
                    print(f"  Checked {idx + 1}/{len(self.addresses)} addresses...")
                
                # Rate limiting to avoid API limits
                time.sleep(0.1)
                
            except Exception as e:
                print(f"⚠️  Error checking balance for {address}: {str(e)}")
                self.balances[address] = {'trx': 0, 'usdt': 0}
        
        print(f"\n✅ Balance check complete. Found {found_count} addresses with balance.")

    def _get_trx_balance(self, address: str) -> float:
        """Get TRX balance from TronGrid API"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and len(data['data']) > 0:
                    # TRX balance is in sun (1 TRX = 1,000,000 sun)
                    balance_sun = data['data'][0].get('balance', 0)
                    return balance_sun / 1_000_000
            return 0.0
        except:
            return 0.0

    def _get_usdt_balance(self, address: str) -> float:
        """Get USDT TRC20 balance from TronGrid API"""
        try:
            url = "https://api.trongrid.io/wallet/triggerconstantcontract"
            
            # Prepare the call to balanceOf(address)
            # Function selector for balanceOf: 0x70a08231
            function_selector = "70a08231"
            # Pad address to 32 bytes (remove T prefix, add leading zeros)
            address_hex = address[1:]  # Remove 'T' prefix
            padded_address = "0" * 24 + address_hex
            parameter = function_selector + padded_address
            
            payload = {
                "owner_address": address,
                "contract_address": self.USDT_CONTRACT,
                "function_selector": "balanceOf(address)",
                "parameter": parameter,
                "visible": True
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'constant_result' in data and len(data['constant_result']) > 0:
                    # USDT has 6 decimals
                    hex_balance = data['constant_result'][0]
                    balance = int(hex_balance, 16) / 1_000_000
                    return balance
            return 0.0
        except:
            return 0.0

    def save_addresses(self, include_mnemonics: bool = False, check_balance: bool = True) -> str:
        """Save addresses and private keys to output file"""
        try:
            # Separate addresses with and without balance
            with_balance = []
            without_balance = []
            
            if check_balance:
                for addr in self.addresses:
                    address = addr['address']
                    if address in self.balances:
                        trx = self.balances[address]['trx']
                        usdt = self.balances[address]['usdt']
                        if trx > 0 or usdt > 0:
                            with_balance.append(addr)
                        else:
                            without_balance.append(addr)
                    else:
                        without_balance.append(addr)
            else:
                without_balance = self.addresses

            # Main output file
            with open(self.output_file, 'w', encoding='utf-8') as output_file:
                output_file.write(f"# TRON (TRX/USDT TRC20) Wallets\n")
                output_file.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                output_file.write(f"# Total: {len(self.addresses)} wallets\n")
                if check_balance:
                    output_file.write(f"# With balance: {len(with_balance)} wallets\n")
                output_file.write(f"# WARNING: This file contains PRIVATE KEYS - Keep secure!\n")
                output_file.write("#" + "="*60 + "\n\n")

                # First, write addresses with balance
                if with_balance:
                    output_file.write("# === ADDRESSES WITH BALANCE ===\n\n")
                    for addr in with_balance:
                        address = addr['address']
                        trx = self.balances[address]['trx']
                        usdt = self.balances[address]['usdt']
                        
                        output_file.write(f"# Wallet #{addr['line']} 💰\n")
                        if include_mnemonics:
                            output_file.write(f"Mnemonic:    {addr['mnemonic']}\n")
                        output_file.write(f"Address:     {address}\n")
                        output_file.write(f"Private Key: {addr['private_key']}\n")
                        output_file.write(f"TRX Balance: {trx:.6f} TRX\n")
                        output_file.write(f"USDT Balance: {usdt:.6f} USDT\n")
                        output_file.write("-" * 60 + "\n")
                    output_file.write("\n")

                # Then write addresses without balance
                if without_balance:
                    output_file.write("# === ADDRESSES WITHOUT BALANCE ===\n\n")
                    for addr in without_balance:
                        output_file.write(f"# Wallet #{addr['line']}\n")
                        if include_mnemonics:
                            output_file.write(f"Mnemonic:    {addr['mnemonic']}\n")
                        output_file.write(f"Address:     {addr['address']}\n")
                        output_file.write(f"Private Key: {addr['private_key']}\n")
                        output_file.write("-" * 60 + "\n")

            print(f"✅ TRON wallets saved to: {self.output_file}")
            
            # Save addresses with balance separately
            if with_balance and check_balance:
                balance_file = self.output_file.replace('.txt', '_with_balance.txt')
                with open(balance_file, 'w', encoding='utf-8') as f:
                    f.write("# TRON Addresses with Balance\n")
                    f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# Total: {len(with_balance)} wallets with balance\n")
                    f.write("# WARNING: Contains PRIVATE KEYS!\n")
                    f.write("#" + "="*60 + "\n\n")
                    
                    for addr in with_balance:
                        address = addr['address']
                        trx = self.balances[address]['trx']
                        usdt = self.balances[address]['usdt']
                        
                        f.write(f"Mnemonic:    {addr['mnemonic']}\n")
                        f.write(f"Address:     {address}\n")
                        f.write(f"Private Key: {addr['private_key']}\n")
                        f.write(f"TRX:         {trx:.6f}\n")
                        f.write(f"USDT:        {usdt:.6f}\n")
                        f.write("="*60 + "\n\n")
                
                print(f"✅ Wallets with balance saved to: {balance_file}")
            
            # CSV format
            csv_file = self.output_file.replace('.txt', '_csv.txt')
            with open(csv_file, 'w', encoding='utf-8') as f:
                # Header
                if include_mnemonics:
                    f.write("mnemonic,address,private_key,trx,usdt\n")
                else:
                    f.write("address,private_key,trx,usdt\n")
                
                # Data
                for addr in self.addresses:
                    address = addr['address']
                    trx = self.balances.get(address, {}).get('trx', 0)
                    usdt = self.balances.get(address, {}).get('usdt', 0)
                    
                    if include_mnemonics:
                        f.write(f"{addr['mnemonic']},{address},{addr['private_key']},{trx},{usdt}\n")
                    else:
                        f.write(f"{address},{addr['private_key']},{trx},{usdt}\n")
            
            print(f"✅ CSV format saved to: {csv_file}")
            
            # Save mnemonics separately if we generated them
            if include_mnemonics:
                mnemonic_file = self.output_file.replace('.txt', '_mnemonics_only.txt')
                with open(mnemonic_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Generated Mnemonics\n")
                    f.write(f"# Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# Total: {len(self.addresses)} mnemonics\n")
                    f.write("#" + "="*60 + "\n\n")
                    
                    for addr in self.addresses:
                        f.write(f"{addr['mnemonic']}\n")
                
                print(f"✅ Mnemonics only saved to: {mnemonic_file}")
            
            return self.output_file

        except Exception as e:
            print(f"❌ Error saving addresses: {str(e)}")
            sys.exit(1)

    def verify_test_case(self) -> bool:
        """Verify with the provided test case"""
        test_mnemonic = "hard split scrap crater tomorrow during panda shoot adjust glance prepare hawk"
        expected_address = "TYGJ2kPVhs5esAJmdDTirQ12wxXnjArcNW"
        
        print("\n🔍 Running verification test...")
        print(f"Mnemonic: {test_mnemonic}")
        print(f"Expected: {expected_address}")
        
        try:
            # Generate seed bytes
            seed_bytes = Bip39SeedGenerator(test_mnemonic).Generate()
            
            # TRON address using BIP44
            bip44_mst = Bip44.FromSeed(seed_bytes, Bip44Coins.TRON)
            bip44_acc = bip44_mst.Purpose().Coin().Account(0)
            bip44_chg = bip44_acc.Change(Bip44Changes.CHAIN_EXT)
            bip44_addr = bip44_chg.AddressIndex(0)
            
            generated_address = bip44_addr.PublicKey().ToAddress()
            private_key = bip44_addr.PrivateKey().Raw().ToHex()
            
            print(f"Generated: {generated_address}")
            print(f"Private Key: {private_key}")
            
            # Check balance
            print("\nChecking balance...")
            trx_balance = self._get_trx_balance(generated_address)
            usdt_balance = self._get_usdt_balance(generated_address)
            print(f"TRX Balance: {trx_balance:.6f}")
            print(f"USDT Balance: {usdt_balance:.6f}")
            
            if generated_address == expected_address:
                print("✅ Verification PASSED!")
                return True
            else:
                print("❌ Verification FAILED!")
                return False
                
        except Exception as e:
            print(f"❌ Verification error: {str(e)}")
            return False

    def run_generation(self, count: int, check_balance: bool = True) -> None:
        """Run generation mode"""
        print("="*80)
        print("🚀 TRON Address Generator v1.3 - Generation Mode")
        print("="*80)
        print(f"📊 Generating: {count} wallets")
        print(f"🌐 Balance check: {'Enabled' if check_balance else 'Disabled'}")
        print(f"📤 Output file: {self.output_file}")
        print("⚠️  WARNING: Output will contain PRIVATE KEYS!")
        print("="*80)

        # Generate new mnemonics
        mnemonics = self.generate_mnemonics(count)

        # Generate addresses and private keys
        self.generate_addresses(mnemonics)
        
        # Check balances if requested
        if check_balance:
            self.check_balances_online()

        # Save to file (include mnemonics for generated ones)
        self.save_addresses(include_mnemonics=True, check_balance=check_balance)

        # Summary
        print("\n" + "="*80)
        print("✅ GENERATION COMPLETE")
        print("="*80)
        print(f"📊 Total wallets generated: {len(self.addresses)}")
        if check_balance:
            with_balance = sum(1 for addr in self.addresses 
                             if self.balances.get(addr['address'], {}).get('trx', 0) > 0 
                             or self.balances.get(addr['address'], {}).get('usdt', 0) > 0)
            print(f"💰 Wallets with balance: {with_balance}")
        print(f"📄 Output files:")
        print(f"   - Full wallets: {self.output_file}")
        print(f"   - CSV format: {self.output_file.replace('.txt', '_csv.txt')}")
        print(f"   - Mnemonics only: {self.output_file.replace('.txt', '_mnemonics_only.txt')}")
        if check_balance:
            with_balance = sum(1 for addr in self.addresses 
                             if self.balances.get(addr['address'], {}).get('trx', 0) > 0 
                             or self.balances.get(addr['address'], {}).get('usdt', 0) > 0)
            if with_balance > 0:
                print(f"   - With balance: {self.output_file.replace('.txt', '_with_balance.txt')}")
        print("⚠️  SECURITY WARNING: Files contain private keys - keep secure!")
        print("="*80)

    def run_from_file(self, input_file: str, check_balance: bool = True) -> None:
        """Run file input mode"""
        print("="*80)
        print("🚀 TRON Address Generator v1.3 - File Input Mode")
        print("="*80)
        print(f"📥 Input file: {input_file}")
        print(f"🌐 Balance check: {'Enabled' if check_balance else 'Disabled'}")
        print(f"📤 Output file: {self.output_file}")
        print("⚠️  WARNING: Output will contain PRIVATE KEYS!")
        print("="*80)

        # Load seeds from file
        seeds = self.load_seeds_from_file(input_file)

        # Generate addresses and private keys
        self.generate_addresses(seeds)
        
        # Check balances if requested
        if check_balance:
            self.check_balances_online()

        # Save to file (no mnemonics for file input)
        self.save_addresses(include_mnemonics=False, check_balance=check_balance)

        # Summary
        print("\n" + "="*80)
        print("✅ GENERATION COMPLETE")
        print("="*80)
        print(f"📊 Total wallets generated: {len(self.addresses)}")
        if check_balance:
            with_balance = sum(1 for addr in self.addresses 
                             if self.balances.get(addr['address'], {}).get('trx', 0) > 0 
                             or self.balances.get(addr['address'], {}).get('usdt', 0) > 0)
            print(f"💰 Wallets with balance: {with_balance}")
        print(f"📄 Output files:")
        print(f"   - Wallets: {self.output_file}")
        print(f"   - CSV format: {self.output_file.replace('.txt', '_csv.txt')}")
        if check_balance:
            with_balance = sum(1 for addr in self.addresses 
                             if self.balances.get(addr['address'], {}).get('trx', 0) > 0 
                             or self.balances.get(addr['address'], {}).get('usdt', 0) > 0)
            if with_balance > 0:
                print(f"   - With balance: {self.output_file.replace('.txt', '_with_balance.txt')}")
        print("⚠️  SECURITY WARNING: Files contain private keys - keep secure!")
        print("="*80)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='TRON Address Generator v1.3 with Balance Checker',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate 10 new wallets and check balances
  %(prog)s -g 10
  
  # Generate without balance check
  %(prog)s -g 10 --no-check
  
  # Generate 100 wallets with custom output
  %(prog)s -g 100 -o my_tron_wallets.txt
  
  # Read seeds from file and check balances
  %(prog)s -f seeds.txt
  
  # Read seeds without balance check
  %(prog)s -f seeds.txt --no-check
  
  # Verify test case
  %(prog)s --verify

Default output file: tron_wallets_[timestamp].txt
Output includes: mnemonic (if generated), address, private key, balances
        """
    )

    # Mode selection
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '-g', '--generate',
        type=int,
        metavar='COUNT',
        help='Generate COUNT new mnemonics and wallets'
    )
    mode_group.add_argument(
        '-f', '--file',
        type=str,
        metavar='FILE',
        help='Read mnemonics from FILE'
    )
    mode_group.add_argument(
        '--verify',
        action='store_true',
        help='Run verification test with known mnemonic'
    )

    # Output file
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='Output file for wallets (default: tron_wallets_[timestamp].txt)'
    )
    
    # Balance check option
    parser.add_argument(
        '--no-check',
        action='store_true',
        help='Skip balance checking'
    )

    return parser.parse_args()

def main():
    """Main entry point"""
    # Parse command line arguments
    args = parse_arguments()

    # Validate arguments
    if not any([args.generate, args.file, args.verify]):
        print("❌ Error: Please specify a mode!")
        print("   Use -g COUNT to generate new wallets")
        print("   Use -f FILE to read from file")
        print("   Use --verify to run verification")
        print("\nRun 'python tron_generator.py -h' for help")
        sys.exit(1)

    # Create generator instance
    generator = TronAddressGenerator(args.output)
    
    # Run appropriate mode
    if args.verify:
        generator.verify_test_case()
    elif args.generate:
        if args.generate <= 0:
            print("❌ Error: COUNT must be positive!")
            sys.exit(1)
        generator.run_generation(args.generate, check_balance=not args.no_check)
    elif args.file:
        generator.run_from_file(args.file, check_balance=not args.no_check)

if __name__ == "__main__":
    main()
