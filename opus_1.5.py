#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🚀 TRON Address Generator with Balance Checker - Version 1.5
================================================================================
📝 Description:
   Fast in-memory TRON address generator with optional SQLite database search.

✨ Features:
   - All operations in memory (no unnecessary file writes)
   - Only outputs CSV when balance found
   - Parallel balance checking for speed via API
   - SQLite database search mode (much faster than API)
   - Real-time balance discovery notifications
   - Support for existing balance databases
   - Database verification for specific test addresses

📦 Requirements:
   pip install bip_utils requests

👨‍💻 Author: Based on <PERSON><PERSON>'s opus_6.1
📅 Created: 2025-08-26
📅 Updated: 2025-08-30 (v1.5 - Added database search and verification features)

🔧 Version History:
   v1.4 - Original fast memory-only mode
   v1.5 - Added SQLite database search functionality and address verification

💡 Usage:
   python opus_1.5.py -g 1000                         # Generate and check via API
   python opus_1.5.py -g 1000 --db balances.db       # Generate and search in DB
   python opus_1.5.py -f seeds.txt --db balances.db  # Check from file in DB
   python opus_1.5.py --db-verify --db balances.db   # Verify test address in DB

🗄️ Database Requirements:
   SQLite database with table 'accounts' containing:
   - address (TEXT PRIMARY KEY) - TRON address
   - trx_balance (REAL) - TRX balance for the address

   Example database structure:
   CREATE TABLE accounts (
       address TEXT PRIMARY KEY,
       trx_balance REAL
   );

================================================================================
"""

import os
import sys
import argparse
import requests
import time
import concurrent.futures
import sqlite3
from datetime import datetime
from typing import List, Dict, Optional
from threading import Lock

# BIP utilities for wallet generation
try:
    from bip_utils import (
        Bip39MnemonicGenerator,
        Bip39SeedGenerator,
        Bip44, Bip44Coins,
        Bip44Changes,
        Bip39WordsNum
    )
except ImportError:
    print("❌ Error: bip_utils not installed!")
    print("📦 Please install: pip install bip_utils")
    sys.exit(1)

class TronAddressGenerator:
    """Fast TRON address generator with parallel balance checking"""
    
    # USDT Contract on TRON
    USDT_CONTRACT = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
    
    # Thread settings for parallel checking
    MAX_WORKERS = 10  # Number of parallel threads
    
    def __init__(self):
        """Initialize generator"""
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.addresses = []
        self.found_balances = []
        self.checked_count = 0
        self.lock = Lock()
        self.db_path = None  # Path to SQLite database for balance lookup

    def generate_mnemonics(self, count: int) -> List[Dict]:
        """Generate new BIP39 mnemonics"""
        print(f"\n🎲 Generating {count} new mnemonics...")
        mnemonics = []
        
        for i in range(count):
            try:
                # Generate 12-word mnemonic
                mnemonic = Bip39MnemonicGenerator().FromWordsNumber(Bip39WordsNum.WORDS_NUM_12)
                words = str(mnemonic).split()
                
                mnemonics.append({
                    'line': i + 1,
                    'mnemonic': str(mnemonic),
                    'preview': f"{words[0]}...{words[-1]}"
                })
                
                if (i + 1) % 1000 == 0:
                    print(f"  Generated {i + 1}/{count} mnemonics...")
                    
            except Exception as e:
                print(f"❌ Error generating mnemonic #{i+1}: {str(e)}")
        
        print(f"✅ Generated {len(mnemonics)} mnemonics")
        return mnemonics

    def load_seeds_from_file(self, input_file: str) -> List[Dict]:
        """Load and validate seed phrases from input file"""
        seeds = []

        if not os.path.exists(input_file):
            print(f"❌ Error: Input file '{input_file}' not found!")
            sys.exit(1)

        print(f"📂 Loading seeds from '{input_file}'...")

        try:
            with open(input_file, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            valid_count = 0
            for line_num, line in enumerate(lines, 1):
                # Skip empty lines and comments
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Validate 12-word mnemonic
                words = line.split()
                if len(words) != 12:
                    continue

                mnemonic = ' '.join(words)
                seeds.append({
                    'line': line_num,
                    'mnemonic': mnemonic,
                    'preview': f"{words[0]}...{words[-1]}"
                })
                valid_count += 1

            print(f"✅ Loaded {valid_count} valid seed phrases")
            return seeds

        except Exception as e:
            print(f"❌ Error reading input file: {str(e)}")
            sys.exit(1)

    def generate_addresses(self, seeds: List[Dict]) -> None:
        """Generate TRON addresses and private keys from seeds"""
        print(f"\n🔐 Generating {len(seeds)} TRON addresses...")

        for idx, seed_data in enumerate(seeds):
            try:
                # Generate seed bytes
                seed_bytes = Bip39SeedGenerator(seed_data['mnemonic']).Generate()

                # TRON address using BIP44 derivation path: m/44'/195'/0'/0/0
                bip44_mst = Bip44.FromSeed(seed_bytes, Bip44Coins.TRON)
                bip44_acc = bip44_mst.Purpose().Coin().Account(0)
                bip44_chg = bip44_acc.Change(Bip44Changes.CHAIN_EXT)
                bip44_addr = bip44_chg.AddressIndex(0)
                
                # Get address and private key
                tron_address = bip44_addr.PublicKey().ToAddress()
                private_key = bip44_addr.PrivateKey().Raw().ToHex()

                self.addresses.append({
                    'mnemonic': seed_data['mnemonic'],
                    'address': tron_address,
                    'private_key': private_key
                })
                
                if (idx + 1) % 1000 == 0:
                    print(f"  Generated {idx + 1}/{len(seeds)} addresses...")

            except Exception as e:
                print(f"❌ Error generating address: {str(e)}")

        print(f"\n✅ Generated {len(self.addresses)} addresses")

    def search_addresses_in_db(self) -> None:
        """Search generated addresses in SQLite database and output only those with balances"""
        if not self.db_path or not os.path.exists(self.db_path):
            print(f"❌ Error: Database file '{self.db_path}' not found!")
            return

        print(f"\n🔍 Searching {len(self.addresses)} addresses in database: {self.db_path}")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            found_count = 0
            for addr_data in self.addresses:
                address = addr_data['address']

                # Search for address in database
                cursor.execute("SELECT trx_balance FROM accounts WHERE address = ?", (address,))
                result = cursor.fetchone()

                if result and result[0] > 0:
                    balance = result[0]
                    found_count += 1

                    # Create result entry
                    wallet_result = {
                        'mnemonic': addr_data['mnemonic'],
                        'address': address,
                        'private_key': addr_data['private_key'],
                        'trx': balance,
                        'usdt': 0.0  # Database only has TRX balances
                    }

                    self.found_balances.append(wallet_result)

                    print(f"\n💰 FOUND IN DATABASE! Address: {address}")
                    print(f"   TRX Balance: {balance:.6f} TRX")

                    # Save immediately when found
                    self.save_found_balance(wallet_result)

            conn.close()

            print(f"\n✅ Database search complete!")
            print(f"📊 Searched: {len(self.addresses)} addresses")
            print(f"💰 Found with balance: {found_count}")

        except Exception as e:
            print(f"❌ Database error: {str(e)}")
            return

    def verify_database_address(self) -> bool:
        """Verify specific address exists in database"""
        test_address = "T9yD3FCKu1gZG2JcvFoo5fKciR1M34fjWq"

        if not self.db_path or not os.path.exists(self.db_path):
            print(f"❌ Error: Database file '{self.db_path}' not found!")
            return False

        print("\n🔍 Running database verification test...")
        print(f"Database: {self.db_path}")
        print(f"Looking for address: {test_address}")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Search for the specific address
            cursor.execute("SELECT trx_balance FROM accounts WHERE address = ?", (test_address,))
            result = cursor.fetchone()

            conn.close()

            if result:
                balance = result[0]
                print(f"✅ Address FOUND in database!")
                print(f"   TRX Balance: {balance:.6f} TRX")
                return True
            else:
                print(f"❌ Address NOT FOUND in database!")
                return False

        except Exception as e:
            print(f"❌ Database verification error: {str(e)}")
            return False

    def check_single_address(self, addr_data: Dict) -> Optional[Dict]:
        """Check balance for a single address"""
        address = addr_data['address']
        
        try:
            # Check TRX balance
            trx_balance = self._get_trx_balance(address)
            
            # Check USDT balance
            usdt_balance = self._get_usdt_balance(address)
            
            # Update checked count
            with self.lock:
                self.checked_count += 1
                if self.checked_count % 100 == 0:
                    print(f"  Checked {self.checked_count}/{len(self.addresses)} addresses...")
            
            # If balance found, add to results
            if trx_balance > 0 or usdt_balance > 0:
                result = {
                    'mnemonic': addr_data['mnemonic'],
                    'address': address,
                    'private_key': addr_data['private_key'],
                    'trx': trx_balance,
                    'usdt': usdt_balance
                }
                
                with self.lock:
                    self.found_balances.append(result)
                    print(f"\n💰 FOUND! Address: {address}")
                    print(f"   TRX: {trx_balance:.6f} TRX")
                    print(f"   USDT: {usdt_balance:.6f} USDT")
                    
                    # Save immediately when found
                    self.save_found_balance(result)
                
                return result
                
        except Exception as e:
            # Silently handle errors to not spam output
            pass
        
        return None

    def check_balances_parallel(self) -> None:
        """Check balances using parallel threads"""
        print(f"\n🌐 Fast-checking {len(self.addresses)} addresses using {self.MAX_WORKERS} threads...")
        
        self.checked_count = 0
        start_time = time.time()
        
        # Use ThreadPoolExecutor for parallel checking
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.MAX_WORKERS) as executor:
            # Submit all addresses for checking
            futures = [executor.submit(self.check_single_address, addr) for addr in self.addresses]
            
            # Wait for all to complete
            concurrent.futures.wait(futures)
        
        elapsed = time.time() - start_time
        rate = len(self.addresses) / elapsed if elapsed > 0 else 0
        
        print(f"\n✅ Checked {len(self.addresses)} addresses in {elapsed:.1f} seconds ({rate:.1f} addr/sec)")
        print(f"\n💰 Found {len(self.found_balances)} addresses with balance")

    def _get_trx_balance(self, address: str) -> float:
        """Get TRX balance from TronGrid API"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and len(data['data']) > 0:
                    # TRX balance is in sun (1 TRX = 1,000,000 sun)
                    balance_sun = data['data'][0].get('balance', 0)
                    return balance_sun / 1_000_000
            return 0.0
        except:
            return 0.0

    def _get_usdt_balance(self, address: str) -> float:
        """Get USDT TRC20 balance from TronGrid API"""
        try:
            url = "https://api.trongrid.io/wallet/triggerconstantcontract"
            
            # Prepare the call to balanceOf(address)
            function_selector = "70a08231"
            address_hex = address[1:]  # Remove 'T' prefix
            padded_address = "0" * 24 + address_hex
            parameter = function_selector + padded_address
            
            payload = {
                "owner_address": address,
                "contract_address": self.USDT_CONTRACT,
                "function_selector": "balanceOf(address)",
                "parameter": parameter,
                "visible": True
            }
            
            response = requests.post(url, json=payload, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if 'constant_result' in data and len(data['constant_result']) > 0:
                    # USDT has 6 decimals
                    hex_balance = data['constant_result'][0]
                    balance = int(hex_balance, 16) / 1_000_000
                    return balance
            return 0.0
        except:
            return 0.0

    def save_found_balance(self, wallet_data: Dict) -> None:
        """Save a found balance immediately to CSV"""
        csv_file = f"tron_balances_{self.timestamp}.csv"
        
        # Check if file exists to add header
        file_exists = os.path.exists(csv_file)
        
        with open(csv_file, 'a', encoding='utf-8') as f:
            if not file_exists:
                # Write header
                f.write("timestamp,mnemonic,address,private_key,trx,usdt\n")
            
            # Write data
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"{timestamp},{wallet_data['mnemonic']},{wallet_data['address']},{wallet_data['private_key']},{wallet_data['trx']},{wallet_data['usdt']}\n")

    def save_final_report(self) -> None:
        """Save final summary report"""
        if not self.found_balances:
            print("\nNo addresses with balance found - no output files created")
            return
            
        # Summary report
        report_file = f"tron_balances_{self.timestamp}_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"TRON Balance Checker Report\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Checked: {len(self.addresses)}\n")
            f.write(f"Found with Balance: {len(self.found_balances)}\n")
            f.write("="*60 + "\n\n")
            
            total_trx = sum(w['trx'] for w in self.found_balances)
            total_usdt = sum(w['usdt'] for w in self.found_balances)
            
            f.write(f"Total TRX: {total_trx:.6f}\n")
            f.write(f"Total USDT: {total_usdt:.6f}\n")
            f.write("="*60 + "\n\n")
            
            for wallet in self.found_balances:
                f.write(f"Address: {wallet['address']}\n")
                f.write(f"TRX: {wallet['trx']:.6f}, USDT: {wallet['usdt']:.6f}\n")
                f.write("-"*40 + "\n")
        
        print(f"\n📄 Balance CSV saved to: tron_balances_{self.timestamp}.csv")
        print(f"\n📄 Summary report saved to: {report_file}")

    def verify_test_case(self) -> bool:
        """Verify with the provided test case"""
        test_mnemonic = "hard split scrap crater tomorrow during panda shoot adjust glance prepare hawk"
        expected_address = "TYGJ2kPVhs5esAJmdDTirQ12wxXnjArcNW"
        
        print("\n🔍 Running verification test...")
        print(f"Mnemonic: {test_mnemonic}")
        print(f"Expected: {expected_address}")
        
        try:
            seed_bytes = Bip39SeedGenerator(test_mnemonic).Generate()
            bip44_mst = Bip44.FromSeed(seed_bytes, Bip44Coins.TRON)
            bip44_acc = bip44_mst.Purpose().Coin().Account(0)
            bip44_chg = bip44_acc.Change(Bip44Changes.CHAIN_EXT)
            bip44_addr = bip44_chg.AddressIndex(0)
            
            generated_address = bip44_addr.PublicKey().ToAddress()
            private_key = bip44_addr.PrivateKey().Raw().ToHex()
            
            print(f"Generated: {generated_address}")
            print(f"Private Key: {private_key}")
            
            # Check balance
            print("\nChecking balance...")
            trx_balance = self._get_trx_balance(generated_address)
            usdt_balance = self._get_usdt_balance(generated_address)
            print(f"TRX Balance: {trx_balance:.6f}")
            print(f"USDT Balance: {usdt_balance:.6f}")
            
            if generated_address == expected_address:
                print("✅ Verification PASSED!")
                return True
            else:
                print("❌ Verification FAILED!")
                return False
                
        except Exception as e:
            print(f"❌ Verification error: {str(e)}")
            return False

    def run_generation(self, count: int) -> None:
        """Run generation mode"""
        print("="*80)
        print("🚀 TRON Address Generator v1.5 - Fast Memory Mode with Database Search")
        print("="*80)
        print(f"📊 Generating: {count} addresses")
        if self.db_path:
            print(f"🔍 Mode: Generate and search in database")
            print(f"📁 Database: {self.db_path}")
        else:
            print(f"🔍 Mode: Check all, save only with balance")
        print("="*80)

        # Generate new mnemonics
        mnemonics = self.generate_mnemonics(count)

        # Generate addresses
        self.generate_addresses(mnemonics)

        # Choose checking method based on database availability
        if self.db_path:
            # Search in database instead of API calls
            self.search_addresses_in_db()
        else:
            # Check balances via API in parallel
            self.check_balances_parallel()

        # Save final report
        self.save_final_report()

        # Summary
        print("\n" + "="*80)
        print("✅ SCAN COMPLETE")
        print("="*80)
        print(f"📊 Total addresses checked: {len(self.addresses)}")
        print(f"💰 Addresses with balance: {len(self.found_balances)}")

        if self.found_balances:
            total_trx = sum(w['trx'] for w in self.found_balances)
            total_usdt = sum(w['usdt'] for w in self.found_balances)
            print(f"💵 Total found: {total_trx:.6f} TRX, {total_usdt:.6f} USDT")
            print(f"📄 Results saved to: tron_balances_{self.timestamp}.csv")
        else:
            print("🔍 No addresses with balance found")

        print("="*80)

    def run_from_file(self, input_file: str) -> None:
        """Run file input mode"""
        print("="*80)
        print("🚀 TRON Address Generator v1.5 - File Input Mode with Database Search")
        print("="*80)
        print(f"📥 Input file: {input_file}")
        if self.db_path:
            print(f"🔍 Mode: Generate and search in database")
            print(f"📁 Database: {self.db_path}")
        else:
            print(f"🔍 Mode: Check all, save only with balance")
        print("="*80)

        # Load seeds from file
        seeds = self.load_seeds_from_file(input_file)

        # Generate addresses
        self.generate_addresses(seeds)

        # Choose checking method based on database availability
        if self.db_path:
            # Search in database instead of API calls
            self.search_addresses_in_db()
        else:
            # Check balances via API in parallel
            self.check_balances_parallel()

        # Save final report
        self.save_final_report()

        # Summary
        print("\n" + "="*80)
        print("✅ SCAN COMPLETE")
        print("="*80)
        print(f"📊 Total addresses checked: {len(self.addresses)}")
        print(f"💰 Addresses with balance: {len(self.found_balances)}")

        if self.found_balances:
            total_trx = sum(w['trx'] for w in self.found_balances)
            total_usdt = sum(w['usdt'] for w in self.found_balances)
            print(f"💵 Total found: {total_trx:.6f} TRX, {total_usdt:.6f} USDT")
            print(f"📄 Results saved to: tron_balances_{self.timestamp}.csv")
        else:
            print("🔍 No addresses with balance found")

        print("="*80)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='TRON Address Generator v1.5 - Fast Memory Mode with Database Search',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate and check 1000 addresses via API
  %(prog)s -g 1000

  # Generate 1000 addresses and search in database
  %(prog)s -g 1000 --db tron_balances-1K.db

  # Read seeds from file and check via API
  %(prog)s -f seeds.txt

  # Read seeds from file and search in database
  %(prog)s -f seeds.txt --db tron_balances-1K.db

  # Verify test case
  %(prog)s --verify

  # Verify specific address exists in database
  %(prog)s --db-verify --db tron_balances-1K.db

Output: Only creates CSV file if addresses with balance are found
Database mode: Much faster than API calls, searches local SQLite database
        """
    )

    # Mode selection
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '-g', '--generate',
        type=int,
        metavar='COUNT',
        help='Generate COUNT new mnemonics and check balances'
    )
    mode_group.add_argument(
        '-f', '--file',
        type=str,
        metavar='FILE',
        help='Read mnemonics from FILE and check balances'
    )
    mode_group.add_argument(
        '--verify',
        action='store_true',
        help='Run verification test with known mnemonic'
    )
    mode_group.add_argument(
        '--db-verify',
        action='store_true',
        help='Verify specific address exists in database'
    )

    # Database search option
    parser.add_argument(
        '--db',
        type=str,
        metavar='DATABASE',
        help='SQLite database file to search for generated addresses (e.g., tron_balances-1K.db)'
    )

    return parser.parse_args()

def main():
    """Main entry point"""
    # Parse command line arguments
    args = parse_arguments()

    # Validate arguments
    if not any([args.generate, args.file, args.verify, args.db_verify]):
        print("❌ Error: Please specify a mode!")
        print("   Use -g COUNT to generate new addresses")
        print("   Use -f FILE to read from file")
        print("   Use --verify to run verification")
        print("   Use --db-verify to verify address in database")
        print("   Use --db DATABASE to search in SQLite database")
        print("\nRun 'python tron_generator.py -h' for help")
        sys.exit(1)

    # Create generator instance
    generator = TronAddressGenerator()

    # Set database path if provided
    if args.db:
        if not os.path.exists(args.db):
            print(f"❌ Error: Database file '{args.db}' not found!")
            sys.exit(1)
        generator.db_path = args.db
        print(f"📁 Database mode enabled: {args.db}")

    # For db-verify, require database parameter
    if args.db_verify:
        if not args.db:
            print("❌ Error: --db-verify requires --db DATABASE parameter!")
            print("   Example: python opus_1.5.py --db-verify --db tron_balances-1K.db")
            sys.exit(1)

    # Run appropriate mode
    if args.verify:
        generator.verify_test_case()
    elif args.db_verify:
        generator.verify_database_address()
    elif args.generate:
        if args.generate <= 0:
            print("❌ Error: COUNT must be positive!")
            sys.exit(1)
        generator.run_generation(args.generate)
    elif args.file:
        generator.run_from_file(args.file)

if __name__ == "__main__":
    main()
