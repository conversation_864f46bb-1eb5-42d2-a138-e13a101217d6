from __future__ import annotations

from coincurve._libsecp256k1 import lib

CONTEXT_NONE = lib.SECP256K1_CONTEXT_NONE
CONTEXT_FLAGS = {
    CONTEXT_NONE,
}

EC_COMPRESSED = lib.SECP256K1_EC_COMPRESSED
EC_UNCOMPRESSED = lib.SECP256K1_EC_UNCOMPRESSED

# Additional flags available from libsecp256k1
# lib.SECP256K1_TAG_PUBKEY_EVEN
# lib.SECP256K1_TAG_PUBKEY_ODD
# lib.SECP256K1_TAG_PUBKEY_UNCOMPRESSED
# lib.SECP256K1_TAG_PUBKEY_HYBRID_EVEN
# lib.SECP256K1_TAG_PUBKEY_HYBRID_ODD
