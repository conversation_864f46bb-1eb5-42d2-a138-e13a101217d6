import requests

API_KEY = "efb7ace6f30bc0dcb74a94ebc5ad45b8f46a2f0af9d462a0353b7dfc3e9ed4c5"
URL = "https://rpc.ankr.com/multichain/{api_key}".format(api_key=API_KEY)

payload = {
    "jsonrpc": "2.0",
    "method": "ankr_getAccountBalance",
    "params": {
        "blockchain": "tron",
        "address": "TJRyS9Vyr2ibUuVU5QK2JkxTtNUNxubtN2"   # normal TRON address (starts with T)
    },
    "id": 1
}

res = requests.post(URL, json=payload)
print(res.json())
